#!/usr/bin/env python3
"""
检查当前应用使用的数据库路径
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings
from app.db.session import engine
import sqlite3

def main():
    print("=== 数据库路径检查 ===")
    print(f"配置中的数据库URL: {settings.DATABASE_URL}")
    
    # 从SQLAlchemy引擎获取实际路径
    db_url = str(engine.url)
    print(f"SQLAlchemy引擎URL: {db_url}")
    
    if db_url.startswith("sqlite:///"):
        db_path = db_url[10:]  # 去掉 "sqlite:///" 前缀
        print(f"实际数据库文件路径: {db_path}")
        
        # 检查文件是否存在
        if os.path.exists(db_path):
            print(f"✅ 数据库文件存在")
            
            # 获取文件信息
            stat = os.stat(db_path)
            print(f"文件大小: {stat.st_size} 字节")
            print(f"最后修改时间: {stat.st_mtime}")
            
            # 检查数据库中的表
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()
                print(f"数据库中的表: {[table[0] for table in tables]}")
                
                # 检查用户数量
                cursor.execute("SELECT COUNT(*) FROM user;")
                user_count = cursor.fetchone()[0]
                print(f"用户数量: {user_count}")
                
                # 检查公司数量
                cursor.execute("SELECT COUNT(*) FROM company;")
                company_count = cursor.fetchone()[0]
                print(f"公司数量: {company_count}")
                
                conn.close()
            except Exception as e:
                print(f"❌ 查询数据库时出错: {e}")
        else:
            print(f"❌ 数据库文件不存在")
    else:
        print("❌ 不是SQLite数据库")

if __name__ == "__main__":
    main()
