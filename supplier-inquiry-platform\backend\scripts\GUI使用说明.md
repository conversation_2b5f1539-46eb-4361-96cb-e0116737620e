# 供应商询价平台 - GUI运维管理工具使用说明

## 快速启动

### 方法1：使用启动脚本（推荐）
```bash
python start_ops_gui.py
```

### 方法2：直接运行
```bash
python ops_manager_gui.py
```

## 界面概览

### 主界面布局
```
┌─────────────────────────────────────────────────────────────────┐
│                 供应商询价平台 - 运维管理工具                      │
├─────────────────────────────────────────────────────────────────┤
│ 服务器状态: 运行中    系统状态: 良好    2025-05-24 14:30:25    │
├─────────────────┬───────────────────────────────────────────────┤
│                 │                                               │
│   功能面板      │              操作日志                          │
│                 │                                               │
│ ┌─缓存管理─┐   │                                               │
│ │扫描缓存   │   │  [14:30:25] INFO: 运维管理工具启动完成        │
│ │清理缓存   │   │  [14:30:26] INFO: 后端根目录: /path/to/...    │
│ │预览清理   │   │  [14:30:27] SUCCESS: 缓存扫描完成             │
│ └──────────┘   │                                               │
│                 │                                               │
│ ┌─服务器管理─┐  │                                               │
│ │启动服务器  │  │                                               │
│ │停止服务器  │  │                                               │
│ │重启服务器  │  │                                               │
│ └───────────┘  │                                               │
│                 │                                               │
│ ┌─健康检查──┐  │                                               │
│ │健康检查   │   │                                               │
│ │导出报告   │   │                                               │
│ │自动刷新   │   │                                               │
│ │一键修复   │   │                                               │
│ └──────────┘   │                                               │
│                 │                                               │
│                 │              ████████████ 进度条               │
│                 │          [清空日志] [保存日志]                 │
└─────────────────┴───────────────────────────────────────────────┘
```

## 功能详解

### 🗂️ 缓存管理
- **扫描缓存**: 扫描所有`__pycache__`目录和`.pyc`文件
- **清理缓存**: 删除所有Python缓存文件（需确认）
- **预览清理**: 查看将要清理的文件（不实际删除）

### ⚙️ 服务器管理
- **端口设置**: 可自定义服务器端口（默认5000）
- **启动服务器**: 启动FastAPI服务器
- **停止服务器**: 停止运行中的服务器进程
- **重启服务器**: 平滑重启服务器（停止+启动）

### 🏥 健康检查
- **健康检查**: 全面检查系统状态
- **导出报告**: 将健康检查结果导出为JSON文件
- **自动刷新**: 每30秒自动刷新系统状态
- **一键修复**: 执行完整的修复流程

### 📋 状态显示
- **服务器状态**: 显示服务器运行状态（运行中/已停止）
- **系统状态**: 显示整体系统健康状况（良好/一般/异常）
- **缓存信息**: 显示当前缓存文件数量和占用空间
- **进程信息**: 显示相关Python进程数量

## 操作流程

### 日常维护流程
1. **启动工具**: `python start_ops_gui.py`
2. **检查状态**: 查看状态栏显示的系统状态
3. **健康检查**: 点击"健康检查"按钮进行全面检查
4. **处理问题**: 根据检查结果执行相应操作

### 问题修复流程（推荐）
1. **一键修复**: 点击"一键修复"按钮
   - 自动清理Python缓存
   - 自动重启服务器
   - 自动运行健康检查
2. **查看日志**: 在操作日志区域查看详细执行过程
3. **确认结果**: 检查状态栏显示是否恢复正常

### 手动修复流程
1. **清理缓存**: 
   - 点击"扫描缓存"查看缓存情况
   - 点击"清理缓存"删除缓存文件
2. **重启服务器**:
   - 点击"重启服务器"或先"停止"再"启动"
3. **验证状态**:
   - 点击"健康检查"验证修复效果

## 日志系统

### 日志级别
- **INFO** (黑色): 一般信息
- **SUCCESS** (绿色): 成功操作
- **WARNING** (橙色): 警告信息
- **ERROR** (红色): 错误信息
- **DEBUG** (蓝色): 调试信息

### 日志操作
- **清空日志**: 清除当前显示的所有日志
- **保存日志**: 将日志内容保存到文本文件

## 状态指示器

### 服务器状态
- 🟢 **运行中**: 服务器正常运行
- 🔴 **已停止**: 服务器未运行

### 系统状态  
- 🟢 **良好**: 80%以上检查通过
- 🟡 **一般**: 60-79%检查通过
- 🔴 **异常**: 60%以下检查通过

### 日志文件状态
- 🟢 **最近活跃**: 1小时内有写入
- 🟡 **历史文件**: 超过1小时未更新

## 高级功能

### 自动刷新
- 启用后每30秒自动刷新系统状态
- 适合长期监控使用
- 可随时关闭

### 报告导出
- 支持导出JSON格式的健康检查报告
- 包含完整的系统信息、API状态、日志状态等
- 便于存档和分析

### 进度显示
- 所有耗时操作都会显示进度条
- 实时反馈操作状态
- 防止重复点击

## 常见问题

### Q: 工具启动失败怎么办？
A: 
1. 确保Python版本 >= 3.7
2. 运行`python start_ops_gui.py`自动安装依赖
3. 检查是否有权限问题

### Q: 服务器启动失败怎么办？
A:
1. 检查端口是否被占用
2. 确认`main.py`文件存在
3. 查看日志区域的错误信息

### Q: 缓存清理失败怎么办？
A:
1. 确保有足够的文件系统权限
2. 先使用"预览清理"查看要删除的文件
3. 手动删除有问题的目录

### Q: 健康检查显示异常怎么办？
A:
1. 先尝试"一键修复"
2. 查看具体的错误信息
3. 根据错误类型采取相应措施

## 技术要求

### 依赖包
- `tkinter`: GUI界面（Python内置）
- `psutil`: 进程和系统监控
- `requests`: HTTP请求

### 系统要求
- Python 3.7+
- Windows/Linux/macOS
- 至少50MB可用内存

## 安全注意事项

1. **权限控制**: 工具需要适当的文件系统权限
2. **进程管理**: 停止服务器会终止相关Python进程
3. **缓存清理**: 会永久删除Python缓存文件
4. **端口监听**: 确保指定端口未被其他程序占用

## 版本信息

- **版本**: 1.0
- **创建日期**: 2025-05-24
- **兼容平台**: Windows, Linux, macOS
- **Python要求**: 3.7+

---

**注意**: 这是为供应商询价平台专门设计的运维工具，建议在使用前先在测试环境中验证功能。 