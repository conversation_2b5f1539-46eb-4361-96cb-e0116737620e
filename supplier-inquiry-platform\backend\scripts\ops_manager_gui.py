#!/usr/bin/env python3
"""
供应商询价平台 - 运维管理工具 (GUI版本)
===============================================
功能：集成缓存清理、服务器重启、健康检查、数据库管理的图形界面工具
作者：运维团队
版本：1.1
创建日期：2025-05-24
更新日期：2025-05-24
"""

import os
import sys
from pathlib import Path # Make sure Path is imported early

# --- Begin Path Adjustment for Import ---
# Assuming this script is in supplier-inquiry-platform/backend/scripts/
# We want to add supplier-inquiry-platform/backend/ to sys.path
# so that 'from app.core.config import settings' can work.
SCRIPT_DIR = Path(__file__).resolve().parent  # scripts directory
BACKEND_DIR = SCRIPT_DIR.parent              # backend directory
if str(BACKEND_DIR) not in sys.path:
    sys.path.insert(0, str(BACKEND_DIR))
# --- End Path Adjustment for Import ---

try:
    from app.core.config import settings
    SETTINGS_IMPORTED = True
except ImportError:
    SETTINGS_IMPORTED = False
    settings = None # Placeholder if import fails

import time
import json
import shutil
import psutil
import requests
import subprocess
import threading
import sqlite3
import tempfile
from datetime import datetime
from typing import Dict, List, Any, Optional

# GUI相关导入
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
from tkinter.font import Font

class CacheManager:
    """缓存管理器"""
    
    def __init__(self, backend_root: str):
        self.backend_root = Path(backend_root)
        self.cache_patterns = [
            "**/__pycache__/**",
            "**/*.pyc",
            "**/*.pyo", 
            "**/.pytest_cache/**",
            "**/logs/*.log",
            "**/temp/**",
            "**/.coverage"
        ]
    
    def scan_cache_files(self) -> Dict[str, Any]:
        """扫描缓存文件"""
        files = []
        total_size = 0
        
        for pattern in self.cache_patterns:
            for path in self.backend_root.glob(pattern):
                if path.is_file():
                    try:
                        size = path.stat().st_size
                        files.append({
                            "path": str(path),
                            "size": size,
                            "modified": path.stat().st_mtime
                        })
                        total_size += size
                    except (OSError, IOError):
                        continue
        
        return {
            "count": len(files),
            "files": files,
            "total_size": total_size
        }
    
    def clear_cache(self, dry_run: bool = False) -> Dict[str, Any]:
        """清理缓存"""
        result = {
            "success": False,
            "deleted_count": 0,
            "freed_space": 0,
            "errors": []
        }
        
        cache_info = self.scan_cache_files()
        
        for file_info in cache_info["files"]:
            try:
                path = Path(file_info["path"])
                if not dry_run and path.exists():
                    if path.is_file():
                        path.unlink()
                    elif path.is_dir():
                        shutil.rmtree(path)
                    
                result["deleted_count"] += 1
                result["freed_space"] += file_info["size"]
                
            except Exception as e:
                result["errors"].append(f"删除 {file_info['path']} 失败: {str(e)}")
        
        result["success"] = len(result["errors"]) == 0
        return result


class ServerManager:
    """服务器管理器"""
    
    def __init__(self, backend_root: str, port: int = 5000):
        self.backend_root = Path(backend_root)
        self.port = port
        self.main_py_path = self.backend_root / "main.py"
    
    def get_processes_by_port(self) -> List[Dict[str, Any]]:
        """获取占用端口的进程"""
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                connections = proc.connections()
                for conn in connections:
                    if conn.laddr.port == self.port:
                        processes.append({
                            "pid": proc.info['pid'],
                            "name": proc.info['name'],
                            "cmdline": ' '.join(proc.info['cmdline'] or [])
                        })
                        break
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
        return processes
    
    def stop_server(self, force: bool = False) -> Dict[str, Any]:
        """停止服务器"""
        result = {"success": False, "stopped_processes": [], "errors": []}
        
        processes = self.get_processes_by_port()
        
        for proc_info in processes:
            try:
                proc = psutil.Process(proc_info["pid"])
                if force:
                    proc.kill()
                else:
                    proc.terminate()
                result["stopped_processes"].append(proc_info)
            except Exception as e:
                result["errors"].append(f"停止进程 {proc_info['pid']} 失败: {str(e)}")
        
        result["success"] = len(result["errors"]) == 0
        return result
    
    def start_server(self) -> Dict[str, Any]:
        """启动服务器"""
        result = {"success": False, "process_id": None, "error": None}
        
        if not self.main_py_path.exists():
            result["error"] = f"main.py 文件不存在: {self.main_py_path}"
            return result
        
        try:
            # Windows使用PowerShell启动
            if sys.platform == "win32":
                cmd = [
                    "powershell.exe", "-Command",
                    f"cd '{self.backend_root}'; python main.py"
                ]
            else:
                cmd = ["python", "main.py"]
            
            process = subprocess.Popen(
                cmd,
                cwd=str(self.backend_root),
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                creationflags=subprocess.CREATE_NEW_CONSOLE if sys.platform == "win32" else 0
            )
            
            result["success"] = True
            result["process_id"] = process.pid
            
        except Exception as e:
            result["error"] = str(e)
        
        return result
    
    def check_server_status(self) -> Dict[str, Any]:
        """检查服务器状态"""
        processes = self.get_processes_by_port()
        return {
            "running": len(processes) > 0,
            "processes": processes,
            "port": self.port
        }


class HealthChecker:
    """健康检查器"""
    
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url
        self.timeout = 10
    
    def check_system_info(self) -> Dict[str, Any]:
        """检查系统信息"""
        return {
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory": psutil.virtual_memory()._asdict(),
            "disk": self._get_disk_usage(),
            "boot_time": psutil.boot_time(),
            "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
        }
    
    def _get_disk_usage(self) -> Dict[str, Any]:
        """获取磁盘使用情况"""
        try:
            usage = psutil.disk_usage('/')
            return {
                "total": usage.total,
                "used": usage.used,
                "free": usage.free,
                "percent": (usage.used / usage.total) * 100
            }
        except:
            return {"error": "无法获取磁盘信息"}
    
    def check_api_endpoints(self) -> Dict[str, Any]:
        """检查API端点"""
        endpoints = [
            "/",
            "/api/health",
            "/api/users/me",
            "/api/companies"
        ]
        
        results = {}
        
        for endpoint in endpoints:
            url = f"{self.base_url}{endpoint}"
            try:
                response = requests.get(url, timeout=self.timeout)
                results[endpoint] = {
                    "status_code": response.status_code,
                    "response_time": response.elapsed.total_seconds(),
                    "success": 200 <= response.status_code < 400
                }
            except requests.RequestException as e:
                results[endpoint] = {
                    "error": str(e),
                    "success": False
                }
        
        return results
    
    def check_log_files(self, backend_root: str) -> Dict[str, Any]:
        """检查日志文件"""
        log_dir = Path(backend_root) / "logs"
        result = {"files": [], "total_size": 0, "errors": []}
        
        if log_dir.exists():
            for log_file in log_dir.glob("*.log"):
                try:
                    stat = log_file.stat()
                    result["files"].append({
                        "name": log_file.name,
                        "size": stat.st_size,
                        "modified": stat.st_mtime
                    })
                    result["total_size"] += stat.st_size
                except Exception as e:
                    result["errors"].append(f"读取 {log_file} 失败: {str(e)}")
        
        return result
    
    def run_comprehensive_check(self, backend_root: str) -> Dict[str, Any]:
        """运行综合检查"""
        return {
            "system_info": self.check_system_info(),
            "api_endpoints": self.check_api_endpoints(),
            "log_files": self.check_log_files(backend_root)
        }


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, backend_root: str):
        self.backend_root = Path(backend_root)
        self.config_path = self.backend_root / "app" / "core" / "config.py"

        db_path_resolved = False
        if SETTINGS_IMPORTED and settings:
            db_url = settings.DATABASE_URL
            if db_url.startswith("sqlite:///"):
                path_str = db_url[len("sqlite:///"):]
                self.db_path = Path(path_str)
                # Our config.py now calculates an absolute path for DEFAULT_DB_PATH,
                # so self.db_path should be absolute here.
                # No complex relative path resolution needed here if config.py is correct.
                db_path_resolved = True
            else:
                # Log or print a warning if the DB_URL from settings is not a SQLite URL
                print(f"Warning: DATABASE_URL from settings ('{db_url}') is not a local SQLite path. Falling back to original DB search.", file=sys.stderr)

        if not db_path_resolved:
            # Fallback to original logic if settings couldn't be used or URL wasn't sqlite
            print("Warning: Could not use settings.DATABASE_URL or import settings. Falling back to original DB search logic.", file=sys.stderr)
            self.db_path = self._original_find_database_path()

    def _original_find_database_path(self) -> Path: # Ensure return type is Path
        """Original logic to find database file path if settings cannot be used."""
        # 查找数据库文件路径
        # 常见的数据库文件位置
        possible_paths = [
            self.backend_root / "supplier_inquiry.db",
            self.backend_root.parent / "supplier_inquiry.db",
            self.backend_root / "app" / "supplier_inquiry.db",
            self.backend_root / "db" / "supplier_inquiry.db"
        ]
        
        for path_obj in possible_paths: # Renamed to avoid conflict with 'path' module if used
            if path_obj.exists():
                return path_obj
        
        # 如果找不到，返回默认路径
        return self.backend_root.parent / "supplier_inquiry.db"
    
    def get_database_info(self) -> Dict[str, Any]:
        """获取数据库基本信息"""
        info = {
            "path": str(self.db_path),
            "exists": self.db_path.exists(),
            "size_mb": 0,
            "modified_time": None,
            "readable": False,
            "writable": False
        }
        
        if self.db_path.exists():
            try:
                stat = self.db_path.stat()
                info.update({
                    "size_mb": stat.st_size / (1024 * 1024),
                    "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                    "readable": os.access(self.db_path, os.R_OK),
                    "writable": os.access(self.db_path, os.W_OK)
                })
            except Exception:
                pass
        
        return info
    
    def test_connection(self) -> Dict[str, Any]:
        """测试数据库连接"""
        result = {
            "success": False,
            "error": None,
            "version": None,
            "encoding": None
        }
        
        try:
            if not self.db_path.exists():
                result["error"] = "数据库文件不存在"
                return result
            
            with sqlite3.connect(str(self.db_path), timeout=5) as conn:
                cursor = conn.cursor()
                
                # 获取SQLite版本
                cursor.execute("SELECT sqlite_version()")
                result["version"] = cursor.fetchone()[0]
                
                # 获取编码
                cursor.execute("PRAGMA encoding")
                result["encoding"] = cursor.fetchone()[0]
                
                # 简单查询测试
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' LIMIT 1")
                
                result["success"] = True
                
        except sqlite3.OperationalError as e:
            result["error"] = f"数据库操作错误: {str(e)}"
        except Exception as e:
            result["error"] = f"连接错误: {str(e)}"
        
        return result
    
    def get_tables_info(self) -> Dict[str, Any]:
        """获取数据库表信息"""
        result = {
            "success": False,
            "tables": [],
            "total_tables": 0,
            "error": None
        }
        
        try:
            with sqlite3.connect(str(self.db_path), timeout=5) as conn:
                cursor = conn.cursor()
                
                # 获取所有表
                cursor.execute("""
                    SELECT name, sql 
                    FROM sqlite_master 
                    WHERE type='table' AND name NOT LIKE 'sqlite_%'
                    ORDER BY name
                """)
                
                tables_data = cursor.fetchall()
                
                for table_name, create_sql in tables_data:
                    # 获取表的记录数
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                        row_count = cursor.fetchone()[0]
                    except:
                        row_count = -1
                    
                    # 获取表结构
                    try:
                        cursor.execute(f"PRAGMA table_info(`{table_name}`)")
                        columns = cursor.fetchall()
                    except:
                        columns = []
                    
                    result["tables"].append({
                        "name": table_name,
                        "row_count": row_count,
                        "column_count": len(columns),
                        "columns": [{"name": col[1], "type": col[2], "notnull": bool(col[3]), "pk": bool(col[5])} for col in columns]
                    })
                
                result["total_tables"] = len(result["tables"])
                result["success"] = True
                
        except Exception as e:
            result["error"] = f"获取表信息失败: {str(e)}"
        
        return result
    
    def check_database_integrity(self) -> Dict[str, Any]:
        """检查数据库完整性"""
        result = {
            "success": False,
            "integrity_check": None,
            "foreign_key_check": None,
            "errors": [],
            "warnings": []
        }
        
        try:
            with sqlite3.connect(str(self.db_path), timeout=10) as conn:
                cursor = conn.cursor()
                
                # 完整性检查
                cursor.execute("PRAGMA integrity_check")
                integrity_result = cursor.fetchall()
                result["integrity_check"] = [row[0] for row in integrity_result]
                
                # 外键检查
                cursor.execute("PRAGMA foreign_key_check")
                fk_errors = cursor.fetchall()
                result["foreign_key_check"] = len(fk_errors) == 0
                
                if fk_errors:
                    result["errors"].extend([f"外键错误: {row}" for row in fk_errors])
                
                # 检查是否有损坏
                if result["integrity_check"] == ["ok"]:
                    result["success"] = True
                else:
                    result["errors"].extend(result["integrity_check"])
                
        except Exception as e:
            result["errors"].append(f"完整性检查失败: {str(e)}")
        
        return result
    
    def backup_database(self, backup_path: str) -> Dict[str, Any]:
        """备份数据库"""
        result = {
            "success": False,
            "backup_path": backup_path,
            "backup_size": 0,
            "error": None
        }
        
        try:
            if not self.db_path.exists():
                result["error"] = "源数据库文件不存在"
                return result
            
            backup_path_obj = Path(backup_path)
            backup_path_obj.parent.mkdir(parents=True, exist_ok=True)
            
            # 使用SQLite的备份API
            with sqlite3.connect(str(self.db_path)) as source_conn:
                with sqlite3.connect(backup_path) as backup_conn:
                    source_conn.backup(backup_conn)
            
            # 验证备份文件
            if backup_path_obj.exists():
                result["backup_size"] = backup_path_obj.stat().st_size / (1024 * 1024)
                result["success"] = True
            else:
                result["error"] = "备份文件创建失败"
                
        except Exception as e:
            result["error"] = f"备份失败: {str(e)}"
        
        return result
    
    def restore_database(self, backup_path: str) -> Dict[str, Any]:
        """恢复数据库"""
        result = {
            "success": False,
            "original_backup": None,
            "error": None
        }
        
        try:
            backup_file = Path(backup_path)
            if not backup_file.exists():
                result["error"] = "备份文件不存在"
                return result
            
            # 创建当前数据库的备份
            if self.db_path.exists():
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                temp_backup = self.db_path.parent / f"temp_backup_{timestamp}.db"
                shutil.copy2(self.db_path, temp_backup)
                result["original_backup"] = str(temp_backup)
            
            # 恢复数据库
            shutil.copy2(backup_file, self.db_path)
            
            # 验证恢复的数据库
            test_result = self.test_connection()
            if test_result["success"]:
                result["success"] = True
            else:
                result["error"] = f"恢复后数据库验证失败: {test_result['error']}"
                
                # 如果验证失败，恢复原始文件
                if result["original_backup"]:
                    shutil.copy2(result["original_backup"], self.db_path)
                    
        except Exception as e:
            result["error"] = f"恢复失败: {str(e)}"
        
        return result
    
    def optimize_database(self) -> Dict[str, Any]:
        """优化数据库"""
        result = {
            "success": False,
            "operations": [],
            "size_before": 0,
            "size_after": 0,
            "error": None
        }
        
        try:
            # 记录优化前的大小
            if self.db_path.exists():
                result["size_before"] = self.db_path.stat().st_size / (1024 * 1024)
            
            with sqlite3.connect(str(self.db_path), timeout=30) as conn:
                cursor = conn.cursor()
                
                # 分析数据库
                cursor.execute("ANALYZE")
                result["operations"].append("数据库分析完成")
                
                # 重建索引
                cursor.execute("REINDEX")
                result["operations"].append("索引重建完成")
                
                # 清理和压缩
                cursor.execute("VACUUM")
                result["operations"].append("数据库压缩完成")
                
                conn.commit()
            
            # 记录优化后的大小
            if self.db_path.exists():
                result["size_after"] = self.db_path.stat().st_size / (1024 * 1024)
            
            result["success"] = True
            
        except Exception as e:
            result["error"] = f"优化失败: {str(e)}"
        
        return result
    
    def get_database_settings(self) -> Dict[str, Any]:
        """获取数据库设置"""
        settings = {}
        
        try:
            with sqlite3.connect(str(self.db_path), timeout=5) as conn:
                cursor = conn.cursor()
                
                # 获取各种PRAGMA设置
                pragmas = [
                    "journal_mode", "synchronous", "cache_size", 
                    "temp_store", "page_size", "auto_vacuum"
                ]
                
                for pragma in pragmas:
                    try:
                        cursor.execute(f"PRAGMA {pragma}")
                        result = cursor.fetchone()
                        settings[pragma] = result[0] if result else None
                    except:
                        settings[pragma] = "Unknown"
                        
        except Exception as e:
            settings["error"] = str(e)
        
        return settings


class OpsManagerGUI:
    """运维管理GUI主类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("供应商询价平台 - 运维管理工具")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)
        
        # 配置
        self.backend_root = self._find_backend_root()
        self.cache_manager = CacheManager(self.backend_root)
        self.server_manager = ServerManager(self.backend_root)
        self.health_checker = HealthChecker()
        self.database_manager = DatabaseManager(self.backend_root)
        
        # 样式
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        # GUI组件
        self.setup_ui()
        
        # 定时刷新
        self.auto_refresh_enabled = False
        self.refresh_interval = 30000  # 30秒
        
        # 启动时检查
        self.update_time()
        self.after_id = self.root.after(1000, self.update_time)
    
    def _find_backend_root(self) -> str:
        """查找后端根目录"""
        current_dir = Path(__file__).parent
        for _ in range(3):  # 向上查找3级
            main_py = current_dir / "main.py"
            if main_py.exists():
                return str(current_dir)
            current_dir = current_dir.parent
        
        # 默认返回scripts的父目录
        return str(Path(__file__).parent.parent)
    
    def setup_ui(self):
        """设置UI界面"""
        # 主容器
        main_container = ttk.Frame(self.root)
        main_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 状态栏
        self.setup_status_bar(main_container)
        
        # 中间部分 - 分为左右两部分
        content_frame = ttk.Frame(main_container)
        content_frame.pack(fill="both", expand=True, pady=10)
        
        # 左侧 - 功能面板
        left_frame = ttk.Frame(content_frame)
        left_frame.pack(side="left", fill="both", expand=True, padx=(0, 5))
        
        # 右侧 - 日志面板
        right_frame = ttk.Frame(content_frame)
        right_frame.pack(side="right", fill="both", expand=True, padx=(5, 0))
        
        # 设置功能面板
        self.setup_function_panels(left_frame)
        
        # 设置日志面板
        self.setup_log_panel(right_frame)
    
    def setup_status_bar(self, parent):
        """设置状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill="x", pady=(0, 10))
        
        self.status_frame = status_frame
        
        # 服务器状态
        ttk.Label(status_frame, text="服务器状态:").pack(side="left")
        self.server_status_var = tk.StringVar(value="检查中...")
        self.server_status_label = ttk.Label(
            status_frame, 
            textvariable=self.server_status_var,
            foreground="orange"
        )
        self.server_status_label.pack(side="left", padx=(5, 20))
        
        # 系统状态
        ttk.Label(status_frame, text="系统状态:").pack(side="left")
        self.system_status_var = tk.StringVar(value="检查中...")
        self.system_status_label = ttk.Label(
            status_frame, 
            textvariable=self.system_status_var,
            foreground="orange"
        )
        self.system_status_label.pack(side="left", padx=(5, 20))
        
        # 数据库状态
        ttk.Label(status_frame, text="数据库状态:").pack(side="left")
        self.database_status_var = tk.StringVar(value="检查中...")
        self.database_status_label = ttk.Label(
            status_frame, 
            textvariable=self.database_status_var,
            foreground="orange"
        )
        self.database_status_label.pack(side="left", padx=(5, 20))
        
        # 时间
        self.time_var = tk.StringVar()
        time_label = ttk.Label(status_frame, textvariable=self.time_var)
        time_label.pack(side="right")
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            status_frame, 
            variable=self.progress_var, 
            maximum=100
        )
        self.progress_bar.pack(side="right", padx=(0, 10), fill="x")
        
        # 统计信息
        ttk.Label(status_frame, text="缓存:").pack(side="right", padx=(20, 5))
        self.cache_count_var = tk.StringVar(value="检查中...")
        ttk.Label(status_frame, textvariable=self.cache_count_var).pack(side="right")
        
        ttk.Label(status_frame, text="进程:").pack(side="right", padx=(20, 5))
        self.process_count_var = tk.StringVar(value="检查中...")
        ttk.Label(status_frame, textvariable=self.process_count_var).pack(side="right")
    
    def setup_function_panels(self, parent):
        """设置功能面板"""
        # 使用滚动框架
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 缓存管理面板
        self.setup_cache_panel(scrollable_frame)
        
        # 服务器管理面板
        self.setup_server_panel(scrollable_frame)
        
        # 健康检查面板
        self.setup_health_panel(scrollable_frame)
        
        # 数据库管理面板
        self.setup_database_panel(scrollable_frame)
    
    def setup_cache_panel(self, parent):
        """设置缓存管理面板"""
        cache_frame = ttk.LabelFrame(parent, text="缓存管理", padding=10)
        cache_frame.pack(fill="x", pady=5)
        
        # 缓存信息
        info_frame = ttk.Frame(cache_frame)
        info_frame.pack(fill="x", pady=2)
        
        ttk.Label(info_frame, text="缓存统计:").pack(side="left")
        self.cache_info_var = tk.StringVar(value="检查中...")
        ttk.Label(info_frame, textvariable=self.cache_info_var).pack(side="left", padx=(5, 0))
        
        # 按钮区域
        button_frame = ttk.Frame(cache_frame)
        button_frame.pack(fill="x", pady=5)
        
        ttk.Button(
            button_frame, 
            text="扫描缓存", 
            command=self.scan_cache
        ).pack(side="left", padx=2)
        
        ttk.Button(
            button_frame, 
            text="清理缓存", 
            command=self.clear_cache
        ).pack(side="left", padx=2)
        
        ttk.Button(
            button_frame, 
            text="预览清理", 
            command=self.preview_clear_cache
        ).pack(side="left", padx=2)
    
    def setup_server_panel(self, parent):
        """设置服务器管理面板"""
        server_frame = ttk.LabelFrame(parent, text="服务器管理", padding=10)
        server_frame.pack(fill="x", pady=5)
        
        # 服务器信息
        info_frame = ttk.Frame(server_frame)
        info_frame.pack(fill="x", pady=2)
        
        ttk.Label(info_frame, text="服务器信息:").pack(side="left")
        self.server_info_var = tk.StringVar(value="检查中...")
        ttk.Label(info_frame, textvariable=self.server_info_var).pack(side="left", padx=(5, 0))
        
        # 按钮区域
        button_frame = ttk.Frame(server_frame)
        button_frame.pack(fill="x", pady=5)
        
        ttk.Button(
            button_frame, 
            text="启动服务器", 
            command=self.start_server
        ).pack(side="left", padx=2)
        
        ttk.Button(
            button_frame, 
            text="停止服务器", 
            command=self.stop_server
        ).pack(side="left", padx=2)
        
        ttk.Button(
            button_frame, 
            text="重启服务器", 
            command=self.restart_server
        ).pack(side="left", padx=2)
    
    def setup_health_panel(self, parent):
        """设置健康检查面板"""
        health_frame = ttk.LabelFrame(parent, text="健康检查", padding=10)
        health_frame.pack(fill="x", pady=5)
        
        # 健康信息
        info_frame = ttk.Frame(health_frame)
        info_frame.pack(fill="x", pady=2)
        
        ttk.Label(info_frame, text="系统健康:").pack(side="left")
        self.health_info_var = tk.StringVar(value="检查中...")
        ttk.Label(info_frame, textvariable=self.health_info_var).pack(side="left", padx=(5, 0))
        
        # 按钮区域
        button_frame = ttk.Frame(health_frame)
        button_frame.pack(fill="x", pady=5)
        
        ttk.Button(
            button_frame, 
            text="运行健康检查", 
            command=self.run_health_check
        ).pack(side="left", padx=2)
        
        ttk.Button(
            button_frame, 
            text="导出健康报告", 
            command=self.export_health_report
        ).pack(side="left", padx=2)
        
        # 自动刷新
        self.auto_refresh_var = tk.BooleanVar()
        auto_refresh_cb = ttk.Checkbutton(
            button_frame,
            text="自动刷新",
            variable=self.auto_refresh_var,
            command=self.toggle_auto_refresh
        )
        auto_refresh_cb.pack(side="left", padx=2)
        
        ttk.Button(
            button_frame, 
            text="一键修复", 
            command=self.one_click_fix
        ).pack(side="left", padx=2)
        
        ttk.Button(
            button_frame, 
            text="刷新状态", 
            command=self.refresh_status
        ).pack(side="left", padx=2)
    
    def setup_database_panel(self, parent):
        """设置数据库管理面板"""
        database_frame = ttk.LabelFrame(parent, text="数据库管理", padding=10)
        database_frame.pack(fill="x", pady=5)
        
        # 数据库信息
        info_frame = ttk.Frame(database_frame)
        info_frame.pack(fill="x", pady=2)
        
        ttk.Label(info_frame, text="数据库信息:").pack(side="left")
        self.database_info_var = tk.StringVar(value="检查中...")
        ttk.Label(info_frame, textvariable=self.database_info_var).pack(side="left", padx=(5, 0))
        
        # 按钮区域 - 第一行
        button_frame1 = ttk.Frame(database_frame)
        button_frame1.pack(fill="x", pady=2)
        
        ttk.Button(
            button_frame1, 
            text="获取数据库信息", 
            command=self.get_database_info
        ).pack(side="left", padx=2)
        
        ttk.Button(
            button_frame1, 
            text="测试数据库连接", 
            command=self.test_connection
        ).pack(side="left", padx=2)
        
        ttk.Button(
            button_frame1, 
            text="获取表信息", 
            command=self.get_tables_info
        ).pack(side="left", padx=2)
        
        ttk.Button(
            button_frame1, 
            text="检查数据库完整性", 
            command=self.check_database_integrity
        ).pack(side="left", padx=2)
        
        # 按钮区域 - 第二行
        button_frame2 = ttk.Frame(database_frame)
        button_frame2.pack(fill="x", pady=2)
        
        ttk.Button(
            button_frame2, 
            text="备份数据库", 
            command=self.backup_database
        ).pack(side="left", padx=2)
        
        ttk.Button(
            button_frame2, 
            text="恢复数据库", 
            command=self.restore_database
        ).pack(side="left", padx=2)
        
        ttk.Button(
            button_frame2, 
            text="优化数据库", 
            command=self.optimize_database
        ).pack(side="left", padx=2)
        
        ttk.Button(
            button_frame2, 
            text="获取数据库设置", 
            command=self.get_database_settings
        ).pack(side="left", padx=2)
    
    def setup_log_panel(self, parent):
        """设置日志面板"""
        log_frame = ttk.LabelFrame(parent, text="操作日志", padding=10)
        log_frame.pack(fill="both", expand=True)
        
        # 日志控制按钮
        log_control_frame = ttk.Frame(log_frame)
        log_control_frame.pack(fill="x", pady=(0, 5))
        
        ttk.Button(
            log_control_frame, 
            text="清空日志", 
            command=self.clear_log
        ).pack(side="left", padx=2)
        
        ttk.Button(
            log_control_frame, 
            text="保存日志", 
            command=self.save_log
        ).pack(side="left", padx=2)
        
        # 日志文本区域
        self.log_text = scrolledtext.ScrolledText(
            log_frame, 
            wrap=tk.WORD, 
            font=("Consolas", 9),
            state=tk.DISABLED
        )
        self.log_text.pack(fill="both", expand=True)
        
        # 配置文本颜色
        self.log_text.tag_configure("INFO", foreground="black")
        self.log_text.tag_configure("SUCCESS", foreground="green")
        self.log_text.tag_configure("WARNING", foreground="orange")
        self.log_text.tag_configure("ERROR", foreground="red")
    
    def log(self, message: str, level: str = "INFO"):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}\n"
        
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, log_entry, level)
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        
        # 限制日志行数
        lines = self.log_text.get("1.0", tk.END).split("\n")
        if len(lines) > 1000:
            self.log_text.config(state=tk.NORMAL)
            self.log_text.delete("1.0", "500.0")
            self.log_text.config(state=tk.DISABLED)
    
    def clear_log(self):
        """清空日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete("1.0", tk.END)
        self.log_text.config(state=tk.DISABLED)
    
    def save_log(self):
        """保存日志"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"ops_log_{timestamp}.txt"
        
        filepath = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
            initialvalue=filename
        )
        
        if filepath:
            try:
                content = self.log_text.get("1.0", tk.END)
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.log(f"日志已保存到: {filepath}", "SUCCESS")
            except Exception as e:
                self.log(f"保存日志失败: {str(e)}", "ERROR")
    
    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_var.set(current_time)
        self.root.after(1000, self.update_time)
    
    def run_in_thread(self, func, *args, **kwargs):
        """在线程中运行函数"""
        def wrapper():
            try:
                func(*args, **kwargs)
            except Exception as e:
                self.log(f"操作失败: {str(e)}", "ERROR")
            finally:
                self.progress_var.set(0)
        
        thread = threading.Thread(target=wrapper, daemon=True)
        thread.start()
    
    # 缓存管理方法
    def scan_cache(self):
        """扫描缓存"""
        self.run_in_thread(self._scan_cache)
    
    def _scan_cache(self):
        """扫描缓存（内部方法）"""
        self.log("开始扫描缓存文件...", "INFO")
        self.progress_var.set(25)
        
        try:
            result = self.cache_manager.scan_cache_files()
            
            total_size_mb = result["total_size"] / (1024 * 1024)
            info_text = f"{result['count']} 项缓存文件, 总大小: {total_size_mb:.2f} MB"
            self.cache_info_var.set(info_text)
            self.cache_count_var.set(f"{result['count']} 项 ({total_size_mb:.1f}MB)")
            
            self.progress_var.set(100)
            self.log(f"缓存扫描完成: {info_text}", "SUCCESS")
            
            # 显示详细信息
            if result["files"]:
                self.log("缓存文件详情:", "INFO")
                for file_info in result["files"][:10]:  # 只显示前10个
                    size_mb = file_info["size"] / (1024 * 1024)
                    self.log(f"  - {file_info['path']} ({size_mb:.3f} MB)", "INFO")
                
                if len(result["files"]) > 10:
                    self.log(f"  ... 还有 {len(result['files']) - 10} 个文件", "INFO")
            
        except Exception as e:
            self.log(f"扫描缓存失败: {str(e)}", "ERROR")
        finally:
            self.progress_var.set(0)
    
    def clear_cache(self):
        """清理缓存"""
        result = messagebox.askyesno("确认清理", "确定要清理所有缓存文件吗？")
        if result:
            self.run_in_thread(self._clear_cache)
    
    def _clear_cache(self):
        """清理缓存（内部方法）"""
        self.log("开始清理缓存...", "INFO")
        self.progress_var.set(25)
        
        try:
            result = self.cache_manager.clear_cache(dry_run=False)
            
            freed_mb = result["freed_space"] / (1024 * 1024)
            
            if result["success"]:
                self.log(f"缓存清理完成!", "SUCCESS")
                self.log(f"删除了 {result['deleted_count']} 个文件", "SUCCESS")
                self.log(f"释放空间: {freed_mb:.2f} MB", "SUCCESS")
                
                # 更新缓存统计
                self.cache_info_var.set("缓存已清理")
                self.cache_count_var.set("0 项 (0MB)")
            else:
                self.log(f"清理过程中有错误:", "WARNING")
                for error in result["errors"]:
                    self.log(f"  - {error}", "ERROR")
            
            self.progress_var.set(100)
            
        except Exception as e:
            self.log(f"清理缓存失败: {str(e)}", "ERROR")
        finally:
            self.progress_var.set(0)
    
    def preview_clear_cache(self):
        """预览清理缓存"""
        self.run_in_thread(self._preview_clear_cache)
    
    def _preview_clear_cache(self):
        """预览清理缓存（内部方法）"""
        self.log("预览缓存清理...", "INFO")
        self.progress_var.set(25)
        
        try:
            result = self.cache_manager.clear_cache(dry_run=True)
            
            freed_mb = result["freed_space"] / (1024 * 1024)
            
            self.log(f"预览结果:", "INFO")
            self.log(f"将删除 {result['deleted_count']} 个文件", "INFO")
            self.log(f"将释放空间: {freed_mb:.2f} MB", "INFO")
            
            if result["errors"]:
                self.log("预期错误:", "WARNING")
                for error in result["errors"]:
                    self.log(f"  - {error}", "WARNING")
            
            self.progress_var.set(100)
            
        except Exception as e:
            self.log(f"预览失败: {str(e)}", "ERROR")
        finally:
            self.progress_var.set(0)
    
    # 服务器管理方法
    def start_server(self):
        """启动服务器"""
        self.run_in_thread(self._start_server)
    
    def _start_server(self):
        """启动服务器（内部方法）"""
        self.log("启动服务器...", "INFO")
        self.progress_var.set(25)
        
        try:
            # 首先检查是否已经在运行
            status = self.server_manager.check_server_status()
            if status["running"]:
                self.log("服务器已经在运行中", "WARNING")
                processes = status["processes"]
                for proc in processes:
                    self.log(f"运行中的进程: PID {proc['pid']} - {proc['name']}", "INFO")
                return
            
            self.progress_var.set(50)
            
            # 启动服务器
            result = self.server_manager.start_server()
            
            if result["success"]:
                self.log("服务器启动成功!", "SUCCESS")
                self.log(f"进程ID: {result['process_id']}", "INFO")
                
                # 等待服务器启动
                self.log("等待服务器启动...", "INFO")
                time.sleep(3)
                
                # 检查启动状态
                status = self.server_manager.check_server_status()
                if status["running"]:
                    self.server_status_var.set("运行中")
                    self.server_status_label.config(foreground="green")
                    self.log("服务器启动验证成功", "SUCCESS")
                else:
                    self.log("服务器可能启动失败，请检查", "WARNING")
            else:
                self.log(f"服务器启动失败: {result['error']}", "ERROR")
            
            self.progress_var.set(100)
            
        except Exception as e:
            self.log(f"启动服务器失败: {str(e)}", "ERROR")
        finally:
            self.progress_var.set(0)
    
    def stop_server(self):
        """停止服务器"""
        self.run_in_thread(self._stop_server)
    
    def _stop_server(self):
        """停止服务器（内部方法）"""
        self.log("停止服务器...", "INFO")
        self.progress_var.set(25)
        
        try:
            # 获取运行中的进程
            processes = self.server_manager.get_processes_by_port()
            if not processes:
                self.log("没有发现运行中的服务器进程", "INFO")
                return
            
            self.progress_var.set(50)
            
            # 停止服务器
            result = self.server_manager.stop_server(force=False)
            
            if result["success"]:
                self.log("服务器停止成功!", "SUCCESS")
                for proc in result["stopped_processes"]:
                    self.log(f"已停止进程: PID {proc['pid']} - {proc['name']}", "INFO")
                
                self.server_status_var.set("已停止")
                self.server_status_label.config(foreground="red")
            else:
                self.log("停止服务器时遇到问题:", "WARNING")
                for error in result["errors"]:
                    self.log(f"  - {error}", "ERROR")
                
                # 尝试强制停止
                if messagebox.askyesno("强制停止", "正常停止失败，是否强制停止服务器？"):
                    force_result = self.server_manager.stop_server(force=True)
                    if force_result["success"]:
                        self.log("强制停止成功", "SUCCESS")
                    else:
                        self.log("强制停止也失败了", "ERROR")
            
            self.progress_var.set(100)
            
        except Exception as e:
            self.log(f"停止服务器失败: {str(e)}", "ERROR")
        finally:
            self.progress_var.set(0)
    
    def restart_server(self):
        """重启服务器"""
        self.run_in_thread(self._restart_server)
    
    def _restart_server(self):
        """重启服务器（内部方法）"""
        self.log("重启服务器...", "INFO")
        self.progress_var.set(10)
        
        try:
            # 停止服务器
            self._stop_server()
            self.progress_var.set(50)
            
            # 等待停止
            time.sleep(2)
            
            # 启动服务器
            self._start_server()
            
            self.log("服务器重启完成", "SUCCESS")
            self.progress_var.set(100)
            
        except Exception as e:
            self.log(f"重启服务器失败: {str(e)}", "ERROR")
        finally:
            self.progress_var.set(0)
    
    # 健康检查方法
    def run_health_check(self):
        """运行健康检查"""
        self.run_in_thread(self._run_health_check)
    
    def _run_health_check(self):
        """运行健康检查（内部方法）"""
        self.log("开始运行健康检查...", "INFO")
        self.progress_var.set(20)
        
        try:
            # 系统信息检查
            self.log("检查系统信息...", "INFO")
            system_info = self.health_checker.check_system_info()
            
            self.log(f"CPU使用率: {system_info['cpu_percent']:.1f}%", "INFO")
            memory = system_info['memory']
            memory_percent = (memory['used'] / memory['total']) * 100
            self.log(f"内存使用率: {memory_percent:.1f}% ({memory['used']//1024//1024} MB / {memory['total']//1024//1024} MB)", "INFO")
            
            if 'disk' in system_info and 'percent' in system_info['disk']:
                self.log(f"磁盘使用率: {system_info['disk']['percent']:.1f}%", "INFO")
            
            self.progress_var.set(50)
            
            # API端点检查
            self.log("检查API端点...", "INFO")
            api_results = self.health_checker.check_api_endpoints()
            
            healthy_endpoints = 0
            for endpoint, result in api_results.items():
                if result.get('success', False):
                    self.log(f"✓ {endpoint}: {result['status_code']} ({result['response_time']:.3f}s)", "SUCCESS")
                    healthy_endpoints += 1
                else:
                    error_msg = result.get('error', f"状态码: {result.get('status_code', 'Unknown')}")
                    self.log(f"✗ {endpoint}: {error_msg}", "ERROR")
            
            self.progress_var.set(80)
            
            # 日志文件检查
            self.log("检查日志文件...", "INFO")
            log_results = self.health_checker.check_log_files(self.backend_root)
            
            if log_results['files']:
                total_log_size_mb = log_results['total_size'] / (1024 * 1024)
                self.log(f"发现 {len(log_results['files'])} 个日志文件, 总大小: {total_log_size_mb:.2f} MB", "INFO")
                
                for log_file in log_results['files']:
                    size_mb = log_file['size'] / (1024 * 1024)
                    modified_time = datetime.fromtimestamp(log_file['modified']).strftime("%Y-%m-%d %H:%M:%S")
                    self.log(f"  - {log_file['name']}: {size_mb:.2f} MB (修改时间: {modified_time})", "INFO")
            else:
                self.log("没有发现日志文件", "WARNING")
            
            if log_results['errors']:
                for error in log_results['errors']:
                    self.log(error, "ERROR")
            
            # 更新健康状态显示
            total_endpoints = len(api_results)
            health_score = (healthy_endpoints / total_endpoints * 100) if total_endpoints > 0 else 0
            
            if health_score >= 80:
                health_status = "良好"
                self.system_status_var.set("良好")
                self.system_status_label.config(foreground="green")
            elif health_score >= 60:
                health_status = "一般"
                self.system_status_var.set("一般")
                self.system_status_label.config(foreground="orange")
            else:
                health_status = "异常"
                self.system_status_var.set("异常")
                self.system_status_label.config(foreground="red")
            
            self.health_info_var.set(f"健康度: {health_score:.0f}% ({health_status})")
            
            self.progress_var.set(100)
            self.log(f"健康检查完成 - 总体状况: {health_status} ({health_score:.0f}%)", "SUCCESS")
            
        except Exception as e:
            self.log(f"健康检查失败: {str(e)}", "ERROR")
        finally:
            self.progress_var.set(0)
    
    def export_health_report(self):
        """导出健康报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"health_report_{timestamp}.json"
        
        filepath = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")],
            initialvalue=filename
        )
        
        if filepath:
            self.run_in_thread(self._export_health_report, filepath)
    
    def _export_health_report(self, filename: str):
        """导出健康报告（内部方法）"""
        self.log("生成健康报告...", "INFO")
        self.progress_var.set(25)
        
        try:
            report = self.health_checker.run_comprehensive_check(self.backend_root)
            
            # 添加时间戳和版本信息
            report["timestamp"] = datetime.now().isoformat()
            report["backend_root"] = self.backend_root
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            self.progress_var.set(100)
            self.log(f"健康报告已导出到: {filename}", "SUCCESS")
            
        except Exception as e:
            self.log(f"导出健康报告失败: {str(e)}", "ERROR")
        finally:
            self.progress_var.set(0)
    
    def toggle_auto_refresh(self):
        """切换自动刷新"""
        self.auto_refresh_enabled = self.auto_refresh_var.get()
        if self.auto_refresh_enabled:
            self.log("自动刷新已启用", "INFO")
            self.auto_refresh()
        else:
            self.log("自动刷新已禁用", "INFO")
    
    def auto_refresh(self):
        """自动刷新"""
        if self.auto_refresh_enabled:
            self.refresh_status()
            self.root.after(self.refresh_interval, self.auto_refresh)
    
    def one_click_fix(self):
        """一键修复"""
        result = messagebox.askyesno("一键修复", "将执行以下操作：\n1. 清理缓存\n2. 重启服务器\n3. 运行健康检查\n\n确定继续吗？")
        if result:
            self.run_in_thread(self._one_click_fix)
    
    def _one_click_fix(self):
        """一键修复（内部方法）"""
        try:
            self.log("开始一键修复...", "INFO")
            
            # 1. 清理缓存
            self.log("步骤 1/3: 清理缓存", "INFO")
            self._clear_cache()
            
            # 2. 重启服务器
            self.log("步骤 2/3: 重启服务器", "INFO")
            self._restart_server()
            
            # 3. 运行健康检查
            self.log("步骤 3/3: 运行健康检查", "INFO")
            self._run_health_check()
            
            self.log("一键修复完成!", "SUCCESS")
            
        except Exception as e:
            self.log(f"一键修复失败: {str(e)}", "ERROR")
            messagebox.showerror("错误", f"一键修复失败: {str(e)}")
    
    def refresh_status(self):
        """刷新状态"""
        self.run_in_thread(self._refresh_status)
    
    def _refresh_status(self):
        """刷新状态（内部方法）"""
        try:
            # 检查服务器状态
            status = self.server_manager.check_server_status()
            if status["running"]:
                self.server_status_var.set("运行中")
                self.server_status_label.config(foreground="green")
            else:
                self.server_status_var.set("已停止")
                self.server_status_label.config(foreground="red")
            
            # 检查进程数量
            processes = self.server_manager.get_processes_by_port()
            self.process_count_var.set(f"{len(processes)} 个进程")
            
            # 扫描缓存
            cache_result = self.cache_manager.scan_cache_files()
            total_size_mb = cache_result["total_size"] / (1024 * 1024)
            self.cache_count_var.set(f"{cache_result['count']} 项 ({total_size_mb:.1f}MB)")
            
            # 检查数据库状态
            db_test = self.database_manager.test_connection()
            if db_test["success"]:
                self.database_status_var.set("正常")
                self.database_status_label.config(foreground="green")
            else:
                self.database_status_var.set("异常")
                self.database_status_label.config(foreground="red")
            
        except Exception as e:
            pass  # 静默处理刷新错误
    
    # 数据库管理方法
    def get_database_info(self):
        """获取数据库信息"""
        self.run_in_thread(self._get_database_info)
    
    def _get_database_info(self):
        """获取数据库信息（内部方法）"""
        self.log("开始获取数据库信息...", "INFO")
        self.progress_var.set(25)
        
        try:
            info = self.database_manager.get_database_info()
            
            info_text = (f"路径: {info['path']}\n"
                        f"存在: {info['exists']}\n"
                        f"大小: {info['size_mb']:.2f}MB\n"
                        f"修改时间: {info['modified_time']}\n"
                        f"可读: {info['readable']}\n"
                        f"可写: {info['writable']}")
            
            self.database_info_var.set(info_text)
            
            self.progress_var.set(100)
            self.log("数据库信息获取完成", "SUCCESS")
            self.log(f"数据库路径: {info['path']}", "INFO")
            self.log(f"数据库大小: {info['size_mb']:.2f}MB", "INFO")
            
        except Exception as e:
            self.log(f"获取数据库信息失败: {str(e)}", "ERROR")
        finally:
            self.progress_var.set(0)
    
    def test_connection(self):
        """测试数据库连接"""
        self.run_in_thread(self._test_connection)
    
    def _test_connection(self):
        """测试数据库连接（内部方法）"""
        self.log("开始测试数据库连接...", "INFO")
        self.progress_var.set(25)
        
        try:
            result = self.database_manager.test_connection()
            
            if result["success"]:
                self.log("数据库连接测试成功!", "SUCCESS")
                self.log(f"SQLite版本: {result['version']}", "INFO")
                self.log(f"编码: {result['encoding']}", "INFO")
                
                # 更新状态
                self.database_status_var.set("正常")
                self.database_status_label.config(foreground="green")
            else:
                self.log(f"数据库连接测试失败: {result['error']}", "ERROR")
                self.database_status_var.set("异常")
                self.database_status_label.config(foreground="red")
            
            self.progress_var.set(100)
            
        except Exception as e:
            self.log(f"测试数据库连接失败: {str(e)}", "ERROR")
        finally:
            self.progress_var.set(0)
    
    def get_tables_info(self):
        """获取数据库表信息"""
        self.run_in_thread(self._get_tables_info)
    
    def _get_tables_info(self):
        """获取数据库表信息（内部方法）"""
        self.log("开始获取数据库表信息...", "INFO")
        self.progress_var.set(25)
        
        try:
            result = self.database_manager.get_tables_info()
            
            if result["success"]:
                self.log(f"数据库表信息获取成功! 共 {result['total_tables']} 个表", "SUCCESS")
                
                for table in result["tables"]:
                    self.log(f"表名: {table['name']}", "INFO")
                    self.log(f"  记录数: {table['row_count']}", "INFO")
                    self.log(f"  字段数: {table['column_count']}", "INFO")
                    
                    # 显示字段信息
                    for col in table["columns"]:
                        pk_marker = " (主键)" if col["pk"] else ""
                        notnull_marker = " (非空)" if col["notnull"] else ""
                        self.log(f"    - {col['name']}: {col['type']}{pk_marker}{notnull_marker}", "INFO")
                    
                    self.log("", "INFO")  # 空行分隔
            else:
                self.log(f"获取表信息失败: {result['error']}", "ERROR")
            
            self.progress_var.set(100)
            
        except Exception as e:
            self.log(f"获取数据库表信息失败: {str(e)}", "ERROR")
        finally:
            self.progress_var.set(0)
    
    def check_database_integrity(self):
        """检查数据库完整性"""
        self.run_in_thread(self._check_database_integrity)
    
    def _check_database_integrity(self):
        """检查数据库完整性（内部方法）"""
        self.log("开始检查数据库完整性...", "INFO")
        self.progress_var.set(25)
        
        try:
            result = self.database_manager.check_database_integrity()
            
            if result["success"]:
                self.log("数据库完整性检查通过!", "SUCCESS")
                self.log(f"完整性检查结果: {result['integrity_check']}", "INFO")
                self.log(f"外键检查通过: {result['foreign_key_check']}", "INFO")
            else:
                self.log("数据库完整性检查发现问题:", "ERROR")
                for error in result["errors"]:
                    self.log(f"  - {error}", "ERROR")
                
                for warning in result["warnings"]:
                    self.log(f"  - {warning}", "WARNING")
            
            self.progress_var.set(100)
            
        except Exception as e:
            self.log(f"检查数据库完整性失败: {str(e)}", "ERROR")
        finally:
            self.progress_var.set(0)
    
    def backup_database(self):
        """备份数据库"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"supplier_inquiry_backup_{timestamp}.db"
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".db",
            filetypes=[("数据库文件", "*.db"), ("所有文件", "*.*")],
            title="备份数据库到...",
            initialvalue=default_filename
        )
        
        if filename:
            self.run_in_thread(self._backup_database, filename)
    
    def _backup_database(self, backup_path: str):
        """备份数据库（内部方法）"""
        self.log("开始备份数据库...", "INFO")
        self.progress_var.set(25)
        
        try:
            result = self.database_manager.backup_database(backup_path)
            
            if result["success"]:
                self.log("数据库备份成功!", "SUCCESS")
                self.log(f"备份路径: {result['backup_path']}", "INFO")
                self.log(f"备份大小: {result['backup_size']:.2f}MB", "INFO")
            else:
                self.log(f"备份失败: {result['error']}", "ERROR")
            
            self.progress_var.set(100)
            
        except Exception as e:
            self.log(f"备份数据库失败: {str(e)}", "ERROR")
        finally:
            self.progress_var.set(0)
    
    def restore_database(self):
        """恢复数据库"""
        filename = filedialog.askopenfilename(
            filetypes=[("数据库文件", "*.db"), ("所有文件", "*.*")],
            title="选择要恢复的数据库备份文件"
        )
        
        if filename:
            result = messagebox.askyesno(
                "确认恢复", 
                "恢复数据库将覆盖当前数据库，此操作不可逆。\n"
                "系统会自动创建当前数据库的临时备份。\n\n"
                "确定要继续吗？"
            )
            if result:
                self.run_in_thread(self._restore_database, filename)
    
    def _restore_database(self, backup_path: str):
        """恢复数据库（内部方法）"""
        self.log("开始恢复数据库...", "INFO")
        self.progress_var.set(25)
        
        try:
            result = self.database_manager.restore_database(backup_path)
            
            if result["success"]:
                self.log("数据库恢复成功!", "SUCCESS")
                if result["original_backup"]:
                    self.log(f"原数据库已备份到: {result['original_backup']}", "INFO")
            else:
                self.log(f"恢复失败: {result['error']}", "ERROR")
            
            self.progress_var.set(100)
            
        except Exception as e:
            self.log(f"恢复数据库失败: {str(e)}", "ERROR")
        finally:
            self.progress_var.set(0)
    
    def optimize_database(self):
        """优化数据库"""
        result = messagebox.askyesno("确认优化", "数据库优化可能需要一些时间，确定要继续吗？")
        if result:
            self.run_in_thread(self._optimize_database)
    
    def _optimize_database(self):
        """优化数据库（内部方法）"""
        self.log("开始优化数据库...", "INFO")
        self.progress_var.set(25)
        
        try:
            result = self.database_manager.optimize_database()
            
            if result["success"]:
                self.log("数据库优化完成!", "SUCCESS")
                for operation in result["operations"]:
                    self.log(f"  - {operation}", "INFO")
                self.log(f"优化前大小: {result['size_before']:.2f}MB", "INFO")
                self.log(f"优化后大小: {result['size_after']:.2f}MB", "INFO")
                
                size_saved = result['size_before'] - result['size_after']
                if size_saved > 0:
                    self.log(f"节省空间: {size_saved:.2f}MB", "SUCCESS")
                else:
                    self.log("数据库大小无明显变化", "INFO")
            else:
                self.log(f"优化失败: {result['error']}", "ERROR")
            
            self.progress_var.set(100)
            
        except Exception as e:
            self.log(f"优化数据库失败: {str(e)}", "ERROR")
        finally:
            self.progress_var.set(0)
    
    def get_database_settings(self):
        """获取数据库设置"""
        self.run_in_thread(self._get_database_settings)
    
    def _get_database_settings(self):
        """获取数据库设置（内部方法）"""
        self.log("开始获取数据库设置...", "INFO")
        self.progress_var.set(25)
        
        try:
            settings = self.database_manager.get_database_settings()
            
            if "error" not in settings:
                self.log("数据库设置获取成功:", "SUCCESS")
                for key, value in settings.items():
                    self.log(f"  {key}: {value}", "INFO")
            else:
                self.log(f"获取数据库设置失败: {settings['error']}", "ERROR")
            
            self.progress_var.set(100)
            
        except Exception as e:
            self.log(f"获取数据库设置失败: {str(e)}", "ERROR")
        finally:
            self.progress_var.set(0)
    
    def run(self):
        """运行应用"""
        self.log("运维管理工具启动完成", "SUCCESS")
        self.log(f"后端根目录: {self.backend_root}", "INFO")
        
        # 初始状态检查
        self.refresh_status()
        
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = OpsManagerGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("启动错误", f"程序启动失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main() 